import { fontFamily } from 'tailwindcss/defaultTheme';
import type { Config } from 'tailwindcss';
import tailwindcssAnimate from 'tailwindcss-animate';

const config: Config = {
  darkMode: 'selector',
  content: ['./src/**/*.{html,js,svelte,ts}'],
  safelist: [
    // Data attribute selectors for components
    'data-[state=checked]:bg-primary',
    'data-[state=unchecked]:bg-input',
    'data-[state=checked]:text-primary-foreground',
    'data-[state=checked]:translate-x-4',
    'data-[state=unchecked]:translate-x-0',
    'data-[highlighted]:bg-accent',
    'data-[highlighted]:text-accent-foreground',
    'data-[disabled]:pointer-events-none',
    'data-[disabled]:opacity-50',
    'data-[disabled=true]:cursor-not-allowed',
    'data-[disabled=true]:opacity-50',

    // Animation data attributes
    'data-[state=open]:animate-in',
    'data-[state=closed]:animate-out',
    'data-[state=closed]:fade-out-0',
    'data-[state=open]:fade-in-0',
    'data-[state=closed]:zoom-out-95',
    'data-[state=open]:zoom-in-95',
    'data-[state=closed]:slide-out-to-left-1/2',
    'data-[state=closed]:slide-out-to-top-[48%]',
    'data-[state=open]:slide-in-from-left-1/2',
    'data-[state=open]:slide-in-from-top-[48%]',
    'data-[side=bottom]:slide-in-from-top-2',
    'data-[side=left]:slide-in-from-right-2',
    'data-[side=right]:slide-in-from-left-2',
    'data-[side=top]:slide-in-from-bottom-2',
    'data-[state=open]:animate-accordion-down',
    'data-[state=closed]:animate-accordion-up',

    // Calendar data attributes
    'data-[selected]:bg-primary',
    'data-[selected]:text-primary-foreground',
    'data-[selected]:hover:bg-primary',
    'data-[selected]:hover:text-primary-foreground',
    'data-[selected]:focus:bg-primary',
    'data-[selected]:focus:text-primary-foreground',
    'data-[selected]:opacity-100',
    'data-[unavailable]:text-destructive-foreground',
    'data-[unavailable]:line-through',
    'data-[outside-month]:text-muted-foreground',
    'data-[outside-month]:pointer-events-none',
    'data-[outside-month]:opacity-50',

    // Resizable data attributes
    'data-[direction=vertical]:flex-col',
    'data-[direction=vertical]:h-px',
    'data-[direction=vertical]:w-full',
    'data-[direction=vertical]:after:left-0',
    'data-[direction=vertical]:after:h-1',
    'data-[direction=vertical]:after:w-full',
    'data-[direction=vertical]:after:-translate-y-1/2',
    'data-[direction=vertical]:after:translate-x-0',

    // Select data attributes
    'data-[placeholder]:text-muted-foreground',
    'data-[state=open]:bg-accent',
    'data-[state=open]:text-muted-foreground',

    // Tabs data attributes
    'data-[state=active]:bg-background',
    'data-[state=active]:text-foreground',
    'data-[state=active]:shadow',

    // Table data attributes
    'data-[state=selected]:bg-muted',

    // Aria selectors
    'aria-selected:bg-accent',
    'aria-selected:text-accent-foreground',

    // Peer selectors
    'peer-disabled:cursor-not-allowed',
    'peer-disabled:opacity-70',

    // Group selectors for Sonner toast
    'group-[.toaster]:bg-background',
    'group-[.toaster]:text-foreground',
    'group-[.toaster]:border-border',
    'group-[.toaster]:shadow-lg',
    'group-[.toast]:text-muted-foreground',
    'group-[.toast]:bg-primary',
    'group-[.toast]:text-primary-foreground',
    'group-[.toast]:bg-muted',
    'group-[.toast]:text-muted-foreground',

    // Complex arbitrary selectors
    '[&>svg]:text-foreground',
    '[&>svg]:absolute',
    '[&>svg]:left-4',
    '[&>svg]:top-4',
    '[&>svg~*]:pl-7',
    '[&>svg]:text-destructive',
    '[&_svg]:pointer-events-none',
    '[&_svg]:size-4',
    '[&_svg]:shrink-0',
    '[&>svg]:size-4',
    '[&>svg]:shrink-0',
    '[&_p]:leading-relaxed',
    '[&[data-state=open]>svg]:rotate-180',
    '[&:has([data-selected])]:bg-accent',
    '[&:has([data-selected][data-outside-month])]:bg-accent/50',
    '[&[data-outside-month][data-selected]]:bg-accent/50',
    '[&[data-outside-month][data-selected]]:text-muted-foreground',
    '[&[data-outside-month][data-selected]]:opacity-30',
    '[&_tr:last-child]:border-0',
    '[&:has([role=checkbox])]:pr-0',
    '[&>[role=checkbox]]:translate-y-[2px]',
    '[&>span]:line-clamp-1',
    '[&[data-direction=vertical]>div]:rotate-90',
    '[&[data-today]:not([data-selected])]:bg-accent',
    '[&[data-today]:not([data-selected])]:text-accent-foreground',

    // Command dialog complex selectors
    '[&_[data-cmdk-group-heading]]:px-2',
    '[&_[data-cmdk-group-heading]]:font-medium',
    '[&_[data-cmdk-group]:not([hidden])_~[data-cmdk-group]]:pt-0',
    '[&_[data-cmdk-group]]:px-2',
    '[&_[data-cmdk-input-wrapper]_svg]:h-5',
    '[&_[data-cmdk-input-wrapper]_svg]:w-5',
    '[&_[data-cmdk-input]]:h-12',
    '[&_[data-cmdk-item]]:px-2',
    '[&_[data-cmdk-item]]:py-3',
    '[&_[data-cmdk-item]_svg]:h-5',
    '[&_[data-cmdk-item]_svg]:w-5',

    // Range calendar specific selectors
    'first:[&:has([data-selected])]:rounded-l-md',
    'last:[&:has([data-selected])]:rounded-r-md',
    '[&:has([data-selected][data-selection-end])]:rounded-r-md',
    '[&:has([data-selected][data-selection-start])]:rounded-l-md',
  ],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))',
        },
      },
      borderRadius: {
        xl: 'calc(var(--radius) + 4px)',
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      fontFamily: {
        sans: [...fontFamily.sans],
      },
      backgroundColor: {
        background: 'hsl(var(--background))',
      },
      textColor: {
        foreground: 'hsl(var(--foreground))',
      },
      borderColor: {
        border: 'hsl(var(--border))',
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--bits-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--bits-accordion-content-height)' },
          to: { height: '0' },
        },
        'caret-blink': {
          '0%,70%,100%': { opacity: '1' },
          '20%,50%': { opacity: '0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        'caret-blink': 'caret-blink 1.25s ease-out infinite',
      },
    },
  },
  plugins: [tailwindcssAnimate],
};

export default config;
